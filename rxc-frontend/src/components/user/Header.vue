<script setup lang="ts">
import { ref } from 'vue'
import productsData from '@/data/products.json'
import { useGlobalSettingsStore } from '@/stores'
import TopBanner from './TopBanner.vue'

const store = useGlobalSettingsStore()

const isMenuOpen = ref<boolean>(false)
const activeMobileDropdown = ref<string | null>(null)
const activeDropdown = ref<string | null>(null)

// Get popular products by category
const getPopularProductByCategory = (categorySlug: string) => {
  return (productsData as any[]).find(
    (product) => product.category_slug === categorySlug && product.is_popular === true,
  ) as any | undefined
}

const toggleDropdown = (menu: string) => {
  if (activeDropdown.value === menu) {
    activeDropdown.value = null
  } else {
    activeDropdown.value = menu
  }
}

// const closeDropdown = () => {
//   activeDropdown.value = null
// }

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
  if (!isMenuOpen.value) {
    activeMobileDropdown.value = null
  }
}

const toggleMobileDropdown = (menu: string) => {
  if (activeMobileDropdown.value === menu) {
    activeMobileDropdown.value = null
  } else {
    activeMobileDropdown.value = menu
  }
}

const closeAllDropdowns = () => {
  activeDropdown.value = null
  activeMobileDropdown.value = null
  isMenuOpen.value = false
}
</script>

<template>
  <TopBanner />

  <!-- navbar -->
  <nav
    class="sticky top-0 z-50 bg-gradient-to-r from-white/95 via-gray-50/95 to-white/95 backdrop-blur-sm border-b border-gray-200/30"
  >
    <!-- Main Navigation -->
    <div class="max-w-7xl mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- Logo -->
        <RouterLink :to="{ name: 'Home' }" class="flex-shrink-0">
          <img
            :src="store.settings?.app_logo"
            :alt="store.settings?.app_name"
            class="max-h-12 max-w-56"
          />
        </RouterLink>

        <!-- Desktop Menu -->
        <div class="hidden lg:flex items-center space-x-8">
          <!-- Weight Loss Dropdown -->
          <div class="relative">
            <button
              @click="toggleDropdown('weight-loss')"
              class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
              :class="{ 'border-b-2 border-gray-900': activeDropdown === 'weight-loss' }"
            >
              <span>Weight Loss</span>
            </button>
          </div>

          <!-- Sexual Health Dropdown -->
          <div class="relative">
            <button
              @click="toggleDropdown('sexual-health')"
              class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
              :class="{ 'border-b-2 border-gray-900': activeDropdown === 'sexual-health' }"
            >
              <span>Sexual Health</span>
            </button>
          </div>

          <!-- Hair Dropdown -->
          <div class="relative">
            <button
              @click="toggleDropdown('hair')"
              class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
              :class="{ 'border-b-2 border-gray-900': activeDropdown === 'hair' }"
            >
              <span>Hair</span>
            </button>
          </div>

          <!-- Top Products Dropdown -->
          <div class="relative">
            <button
              @click="toggleDropdown('top-products')"
              class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
              :class="{ 'border-b-2 border-gray-900': activeDropdown === 'top-products' }"
            >
              <span>Top Products</span>
            </button>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="hidden lg:flex items-center space-x-4">
          <RouterLink
            :to="{ name: 'Treatments' }"
            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors"
          >
            Get Care
          </RouterLink>
        </div>

        <!-- Mobile Menu Button -->
        <button @click="toggleMenu" class="lg:hidden p-2">
          <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
            <div
              class="w-5 h-0.5 bg-gray-600 transition-all"
              :class="{
                'rotate-45 translate-y-1.5': isMenuOpen,
              }"
            ></div>
            <div
              class="w-5 h-0.5 bg-gray-600 transition-all"
              :class="{ 'opacity-0': isMenuOpen }"
            ></div>
            <div
              class="w-5 h-0.5 bg-gray-600 transition-all"
              :class="{
                '-rotate-45 -translate-y-1.5': isMenuOpen,
              }"
            ></div>
          </div>
        </button>
      </div>

      <!-- Mobile Menu -->
      <transition name="slide-down">
        <div v-if="isMenuOpen" class="lg:hidden inset-0 z-50 flex flex-col bg-white">
          <!-- Menu content scrolls -->
          <div class="flex-1 overflow-y-auto px-4 pt-6 pb-10">
            <div class="flex flex-col gap-6">
              <button
                @click="toggleMobileDropdown('weight-loss')"
                class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'weight-loss' }"
              >
                <span>Weight Loss</span>
                <svg
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': activeMobileDropdown === 'weight-loss' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div v-show="activeMobileDropdown === 'weight-loss'" class="pl-4 mt-2 space-y-2">
                <div>
                  <div class="text-xs text-gray-500 mb-1">Treatments</div>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'semaglutide' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Compounded Semaglutide <span class="align-super text-xs">Rx</span></RouterLink
                  >
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'tirzepatide' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Compounded Tirzepatide <span class="align-super text-xs">Rx</span></RouterLink
                  >
                </div>
                <div>
                  <div class="text-xs text-gray-500 mb-1">Name Brand</div>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'wegovy' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                  >
                    Wegovy <span class="align-super text-xs">Rx</span></RouterLink
                  >
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'ozempic' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                  >
                    Ozempic <span class="align-super text-xs">Rx</span></RouterLink
                  >
                </div>
                <div v-if="getPopularProductByCategory('weight-loss')" class="pt-2">
                  <div class="text-xs text-gray-500 mb-1">Best Selling</div>
                  <div @click="closeAllDropdowns" class="cursor-pointer">
                    <RouterLink
                      :to="{
                        name: 'TreatmentOverview',
                        params: { slug: getPopularProductByCategory('weight-loss').slug },
                      }"
                      class="block"
                    >
                      <div
                        class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group"
                      >
                        <h4 class="font-medium text-gray-900 mb-1">
                          {{ getPopularProductByCategory('weight-loss').name }}
                        </h4>
                        <p class="text-xs text-gray-600 mb-3">
                          {{ getPopularProductByCategory('weight-loss').product_type }}
                        </p>
                        <img
                          :src="getPopularProductByCategory('weight-loss').product_image"
                          :alt="getPopularProductByCategory('weight-loss').name"
                          class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
                        />
                        <span
                          class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                        >
                          {{ getPopularProductByCategory('weight-loss').category }}
                        </span>
                      </div>
                    </RouterLink>
                  </div>
                </div>
              </div>
              <button
                @click="toggleMobileDropdown('sexual-health')"
                class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'sexual-health' }"
              >
                <span>Sexual Health</span>
                <svg
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': activeMobileDropdown === 'sexual-health' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div v-show="activeMobileDropdown === 'sexual-health'" class="pl-4 mt-2 space-y-2">
                <div>
                  <div class="text-xs text-gray-500 mb-1">Treatments</div>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'tadalafil-cialis' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Tadalafil (Generic Cialis)
                    <span class="align-super text-xs">Rx</span></RouterLink
                  >
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'sildenafil-viagra' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Sildenafil (Generic Viagra)
                    <span class="align-super text-xs">Rx</span></RouterLink
                  >
                </div>
                <div v-if="getPopularProductByCategory('sexual-health')" class="pt-2">
                  <div class="text-xs text-gray-500 mb-1">Best Selling</div>
                  <div @click="closeAllDropdowns" class="cursor-pointer">
                    <RouterLink
                      :to="{
                        name: 'TreatmentOverview',
                        params: { slug: getPopularProductByCategory('sexual-health').slug },
                      }"
                      class="block"
                    >
                      <div
                        class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group"
                      >
                        <h4 class="font-medium text-gray-900 mb-1">
                          {{ getPopularProductByCategory('sexual-health').name }}
                        </h4>
                        <p class="text-xs text-gray-600 mb-3">
                          {{ getPopularProductByCategory('sexual-health').product_type }}
                        </p>
                        <img
                          :src="getPopularProductByCategory('sexual-health').product_image"
                          :alt="getPopularProductByCategory('sexual-health').name"
                          class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
                        />
                        <span
                          class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                        >
                          {{ getPopularProductByCategory('sexual-health').category }}
                        </span>
                      </div>
                    </RouterLink>
                  </div>
                </div>
              </div>
              <button
                @click="toggleMobileDropdown('hair')"
                class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'hair' }"
              >
                <span>Hair</span>
                <svg
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': activeMobileDropdown === 'hair' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div v-show="activeMobileDropdown === 'hair'" class="pl-4 mt-2 space-y-2">
                <div>
                  <div class="text-xs text-gray-500 mb-1">Treatments</div>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'finasteride' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Finasteride <span class="align-super text-xs">Rx</span>
                  </RouterLink>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'minoxidil' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Minoxidil</RouterLink
                  >
                </div>
                <div v-if="getPopularProductByCategory('hair-health')" class="pt-2">
                  <div class="text-xs text-gray-500 mb-1">Best Selling</div>
                  <div @click="closeAllDropdowns" class="cursor-pointer">
                    <RouterLink
                      :to="{
                        name: 'TreatmentOverview',
                        params: { slug: getPopularProductByCategory('hair-health').slug },
                      }"
                      class="block"
                    >
                      <div
                        class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group"
                      >
                        <h4 class="font-medium text-gray-900 mb-1">
                          {{ getPopularProductByCategory('hair-health').name }}
                        </h4>
                        <p class="text-xs text-gray-600 mb-3">
                          {{ getPopularProductByCategory('hair-health').product_type }}
                        </p>
                        <img
                          :src="getPopularProductByCategory('hair-health').product_image"
                          :alt="getPopularProductByCategory('hair-health').name"
                          class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
                        />
                        <span
                          class="inline-block px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                        >
                          {{ getPopularProductByCategory('hair-health').category }}
                        </span>
                      </div>
                    </RouterLink>
                  </div>
                </div>
              </div>

              <button
                @click="toggleMobileDropdown('top-products')"
                class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'top-products' }"
              >
                <span>Top Products</span>
                <svg
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': activeMobileDropdown === 'top-products' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </button>
              <div v-show="activeMobileDropdown === 'top-products'" class="pl-4 mt-2 space-y-2">
                <div>
                  <div class="text-xs text-gray-500 mb-1">Featured Products</div>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'semaglutide' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Semaglutide <span class="align-super text-xs">Rx</span>
                  </RouterLink>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'tadalafil-cialis' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Tadalafil <span class="align-super text-xs">Rx</span>
                  </RouterLink>
                  <RouterLink
                    :to="{ name: 'TreatmentOverview', params: { slug: 'finasteride' } }"
                    @click="closeAllDropdowns"
                    class="block text-gray-800 py-1"
                    >Finasteride <span class="align-super text-xs">Rx</span>
                  </RouterLink>
                </div>
              </div>
              <RouterLink
                :to="{ name: 'Treatments' }"
                class="block w-full mt-6 py-3 text-center text-gray-900 border-2 border-gray-900 rounded-lg hover:bg-gray-900 hover:text-white transition-colors"
              >
                Get Care
              </RouterLink>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </nav>

  <!-- Mega Menu Dropdowns -->
  <!-- <div
    v-if="activeDropdown"
    @click="closeDropdown"
    class="fixed inset-0 bg-black/10 backdrop-blur-[2px] z-40"
  ></div> -->

  <!-- Weight Loss Dropdown -->
  <div
    v-show="activeDropdown === 'weight-loss'"
    class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm"
  >
    <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'Treatments', params: { category_slug: 'weight-loss' } }"
              @click="closeAllDropdowns"
              class="text-lg text-gray-800 hover:underline transition-all"
              >Weight Loss
            </RouterLink>
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'semaglutide' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Compounded Semaglutide <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'tirzepatide' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Compounded Tirzepatide <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Name Brand</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'wegovy' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Wegovy <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'ozempic' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Ozempic <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div v-if="getPopularProductByCategory('weight-loss')">
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
        <div @click="closeAllDropdowns" class="cursor-pointer">
          <RouterLink
            :to="{
              name: 'TreatmentOverview',
              params: { slug: getPopularProductByCategory('weight-loss').slug },
            }"
            class="block"
          >
            <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group">
              <h4 class="font-medium text-gray-900 mb-1">
                {{ getPopularProductByCategory('weight-loss').name }}
              </h4>
              <p class="text-xs text-gray-600 mb-3">
                {{ getPopularProductByCategory('weight-loss').product_type }}
              </p>
              <img
                :src="getPopularProductByCategory('weight-loss').product_image"
                :alt="getPopularProductByCategory('weight-loss').name"
                class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
              />
              <span class="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                {{ getPopularProductByCategory('weight-loss').category }}
              </span>
            </div>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>

  <!-- Sexual Health Dropdown -->
  <div
    v-show="activeDropdown === 'sexual-health'"
    class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm"
  >
    <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'Treatments', params: { category_slug: 'sexual-health' } }"
              @click="closeAllDropdowns"
              class="text-lg text-gray-800 hover:underline transition-all"
              >Sexual Health
            </RouterLink>
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'tadalafil-cialis' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Tadalafil (Generic Cialis) <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'sildenafil-viagra' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Sildenafil (Generic Viagra) <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Name Brand</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'branded-viagra' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Viagra <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'branded-cialis' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Cialis <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div v-if="getPopularProductByCategory('sexual-health')">
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
        <div @click="closeAllDropdowns" class="cursor-pointer">
          <RouterLink
            :to="{
              name: 'TreatmentOverview',
              params: { slug: getPopularProductByCategory('sexual-health').slug },
            }"
            class="block"
          >
            <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group">
              <h4 class="font-medium text-gray-900 mb-1">
                {{ getPopularProductByCategory('sexual-health').name }}
              </h4>
              <p class="text-xs text-gray-600 mb-3">
                {{ getPopularProductByCategory('sexual-health').product_type }}
              </p>
              <img
                :src="getPopularProductByCategory('sexual-health').product_image"
                :alt="getPopularProductByCategory('sexual-health').name"
                class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
              />
              <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {{ getPopularProductByCategory('sexual-health').category }}
              </span>
            </div>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>

  <!-- Hair Dropdown -->
  <div
    v-show="activeDropdown === 'hair'"
    class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm"
  >
    <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'Treatments', params: { category_slug: 'hair-health' } }"
              @click="closeAllDropdowns"
              class="text-lg text-gray-800 hover:underline transition-all"
              >Hair
            </RouterLink>
          </li>
        </ul>
      </div>
      <div class="col-span-2">
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'finasteride' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Oral Finasteride
              <span class="align-super text-xs">Rx</span>
            </RouterLink>
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'minoxidil' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Oral Minoxidil <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div v-if="getPopularProductByCategory('hair-health')">
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
        <div @click="closeAllDropdowns" class="cursor-pointer">
          <RouterLink
            :to="{
              name: 'TreatmentOverview',
              params: { slug: getPopularProductByCategory('hair-health').slug },
            }"
            class="block"
          >
            <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group">
              <h4 class="font-medium text-gray-900 mb-1">
                {{ getPopularProductByCategory('hair-health').name }}
              </h4>
              <p class="text-xs text-gray-600 mb-3">
                {{ getPopularProductByCategory('hair-health').product_type }}
              </p>
              <img
                :src="getPopularProductByCategory('hair-health').product_image"
                :alt="getPopularProductByCategory('hair-health').name"
                class="w-full h-32 object-contain rounded-lg mb-3 group-hover:scale-105 transition-transform"
              />
              <span
                class="inline-block px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
              >
                {{ getPopularProductByCategory('hair-health').category }}
              </span>
            </div>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Products Dropdown -->
  <div
    v-show="activeDropdown === 'top-products'"
    class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm"
  >
    <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-3 gap-8">
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Weight Loss</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'semaglutide' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Compounded Semaglutide <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'tirzepatide' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Compounded Tirzepatide <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Sexual Health</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'tadalafil-cialis' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Tadalafil (Generic Cialis) <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'sildenafil-viagra' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Sildenafil (Generic Viagra) <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
      <div>
        <h3 class="text-sm font-semibold text-gray-600 mb-5">Hair</h3>
        <ul class="space-y-4">
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'finasteride' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Oral Finasteride
              <span class="align-super text-xs">Rx</span>
            </RouterLink>
          </li>
          <li>
            <RouterLink
              :to="{ name: 'TreatmentOverview', params: { slug: 'minoxidil' } }"
              @click="closeAllDropdowns"
              class="text-sm text-gray-800 hover:underline transition-all"
              >Oral Minoxidil <span class="align-super text-xs">Rx</span></RouterLink
            >
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
