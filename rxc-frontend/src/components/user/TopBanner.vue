<template>
  <div
    class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50 overflow-hidden py-2 z-50"
  >
    <div class="relative">
      <div class="flex animate-ticker whitespace-nowrap">
        <!-- First set of items -->
        <div class="flex items-center space-x-16 sm:space-x-28 pr-16 sm:pr-28 flex-shrink-0">
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/us-flag.png" alt="US Flag" class="w-4 h-4 grayscale" />
            <span class="text-xs">US sourced ingredients</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/star.png" alt="Star" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">Trusted by over 100K subscribers</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/laptop.png" alt="Laptop" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">100% online process</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/user.png" alt="User" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">No membership requirements</span>
          </div>
          <div class="flex items-center space-x-2">
            <img
              src="@/assets/images/no-hidden-fees.png"
              alt="User"
              class="w-4 h-4 grayscale invert"
            />
            <span class="text-xs">Transparent pricing, no hidden fees</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/prescription.png" alt="User" class="w-4 h-4 grayscale" />
            <span class="text-xs">FDA regulated pharmacies</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/physician.png" alt="User" class="w-4 h-4 grayscale" />
            <span class="text-xs">Board certified physicians</span>
          </div>
        </div>

        <!-- Duplicate set for seamless loop -->
        <div class="flex items-center space-x-16 sm:space-x-28 pr-16 sm:pr-28 flex-shrink-0">
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/us-flag.png" alt="US Flag" class="w-4 h-4 grayscale" />
            <span class="text-xs">US sourced ingredients</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/star.png" alt="Star" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">Trusted by over 100K subscribers</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/laptop.png" alt="Laptop" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">100% online process</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/user.png" alt="User" class="w-4 h-4 grayscale invert" />
            <span class="text-xs">No membership requirements</span>
          </div>
          <div class="flex items-center space-x-2">
            <img
              src="@/assets/images/no-hidden-fees.png"
              alt="User"
              class="w-4 h-4 grayscale invert"
            />
            <span class="text-xs">Transparent pricing, no hidden fees</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/prescription.png" alt="User" class="w-4 h-4 grayscale" />
            <span class="text-xs">FDA regulated pharmacies</span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="@/assets/images/physician.png" alt="User" class="w-4 h-4 grayscale" />
            <span class="text-xs">Board certified physicians</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes ticker {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-ticker {
  animation: ticker 30s linear infinite;
}

/* Pause animation on hover for better UX */
.animate-ticker:hover {
  animation-play-state: paused;
}

@media screen and (max-width: 767px) {
  .animate-ticker {
    animation: ticker 20s linear infinite;
  }
}

@media screen and (min-width: 1024px) {
  .animate-ticker {
    animation: ticker 40s linear infinite;
  }
}
</style>
